// Global state to track automation status
let automationStatus = {
    state: 'running', // 'running' or 'paused'
    lastUpdated: Date.now()
};

// Helper function to broadcast automation status to all content scripts
function broadcastAutomationStatus() {
    chrome.tabs.query({url: "https://www.irctc.co.in/*"}, function(tabs) {
        tabs.forEach(tab => {
            chrome.tabs.sendMessage(tab.id, {action: 'updateAutomationStatus', status: automationStatus});
        });
    });
}

// Function to handle booking start request
async function handleStartBooking(request, sendResponse) {
    try {
        console.log('Background: Starting booking process');

        // Create new IRCTC tab
        const tab = await chrome.tabs.create({
            url: 'https://www.irctc.co.in/nget/train-search',
            active: true
        });

        console.log('Background: Created IRCTC tab with ID:', tab.id);

        // Wait for tab to load and inject scripts
        chrome.tabs.onUpdated.addListener(function tabUpdateListener(tabId, changeInfo, updatedTab) {
            if (tabId === tab.id && changeInfo.status === 'complete') {
                console.log('Background: IRCTC tab loaded, injecting scripts');
                chrome.tabs.onUpdated.removeListener(tabUpdateListener);

                // Inject the content script
                injectContentScript(tab.id, sendResponse);
            }
        });

    } catch (error) {
        console.error('Background: Error starting booking:', error);
        sendResponse({ status: 'error', message: error.message });
    }
}

// Function to inject content script and start booking
async function injectContentScript(tabId, sendResponse) {
    try {
        console.log('Background: Injecting content script into tab:', tabId);

        // First inject config.js
        await chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ['config.js']
        });

        // Then inject content_script.js
        await chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ['content_script.js']
        });

        console.log('Background: Scripts injected successfully');

        // Wait a moment for scripts to initialize
        setTimeout(() => {
            // Send start booking message to the injected content script
            chrome.tabs.sendMessage(tabId, {
                action: 'startBooking'
            }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('Background: Error sending start booking message:', chrome.runtime.lastError);
                    sendResponse({ status: 'error', message: 'Failed to start booking automation' });
                } else {
                    console.log('Background: Booking started successfully');
                    sendResponse({ status: 'success', message: 'Booking automation started' });
                }
            });
        }, 2000); // Wait 2 seconds for content script to initialize

    } catch (error) {
        console.error('Background: Error injecting content script:', error);
        sendResponse({ status: 'error', message: 'Failed to inject automation script' });
    }
}

// Handle various extension messages
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'openPopup') {
        chrome.action.openPopup();
    }
    else if (request.action === 'pauseAutomation') {
        automationStatus.state = 'paused';
        automationStatus.lastUpdated = Date.now();
        sendResponse({ status: 'success', currentState: automationStatus.state });
        // Broadcast to all content scripts
        broadcastAutomationStatus();
    }
    else if (request.action === 'resumeAutomation') {
        automationStatus.state = 'running';
        automationStatus.lastUpdated = Date.now();
        sendResponse({ status: 'success', currentState: automationStatus.state });
        // Broadcast to all content scripts
        broadcastAutomationStatus();
    }
    else if (request.action === 'getAutomationStatus') {
        sendResponse({ status: 'success', automationStatus: automationStatus });
    }
    else if (request.action === 'startBooking') {
        console.log('Background: Received start booking request');
        handleStartBooking(request, sendResponse);
        return true; // Keep message channel open for async response
    }

    return true; // Required for async sendResponse
});

// Handle installation or update
chrome.runtime.onInstalled.addListener((details) => {
    if (details.reason === 'install') {
        // Open welcome page or dashboard on fresh install
        chrome.tabs.create({
            url: chrome.runtime.getURL('popup.html'),
            active: true
        });
    }
});

// Handle extension icon click when already logged in
chrome.action.onClicked.addListener((tab) => {
    chrome.storage.local.get(['isLoggedIn'], function(result) {
        if (result.isLoggedIn) {
            // If logged in, open the controls popup
            chrome.tabs.create({
                url: chrome.runtime.getURL('popup-controls.html'),
                active: true
            });
        } else {
            // If not logged in, open the regular popup
            chrome.action.openPopup();
        }
    });
});


