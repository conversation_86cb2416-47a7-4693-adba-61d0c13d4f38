

/* Navbar styles */
.navbar {
    background: white;
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e1e4e8;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
}

.user-details {
    font-size: 12px;
}

.user-details h2 {
    font-size: 13px;
    margin: 0;
}

.user-details p {
    font-size: 11px;
    margin: 2px 0 0;
}

.btn-logout {
    background: none;
    border: none;
    color: #57606a;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
}

.btn-logout:hover {
    background: #f6f8fa;
    color: #24292f;
}

/* Main content layout */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar styles */
.sidebar {
    width: 50px;
    background: white;
    border-right: 1px solid #e1e4e8;
    transition: width 0.3s ease;
    overflow: hidden;
}

.sidebar:hover {
    width: 200px;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: background 0.2s ease;
    white-space: nowrap;
}

.menu-item i {
    width: 20px;
    text-align: center;
    margin-right: 12px;
}

.menu-item span {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.sidebar:hover .menu-item span {
    opacity: 1;
}

.menu-item:hover {
    background: #f6f8fa;
}

.menu-item.active {
    background: #0969da;
    color: white;
}

/* Content area styles */
.content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    background: #f6f8fa;
}

/* Section content styles */
.section-content {
    background: white;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #24292f;
}

/* Dashboard cards */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.card {
    background: white;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Responsive adjustments */
@media (max-width: 400px) {
    .sidebar {
        width: 40px;
    }

    .content {
        padding: 16px;
    }
}

/* Loading states */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Error states */
.error {
    color: #d32f2f;
    font-size: 12px;
    margin-top: 4px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 12px;
}

.user-profile-section {
    background: white;
    border-radius: 10px;
    padding: 12px;
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-right: 20px;
}

.user-details h2 {
    margin: 0;
    font-size: 20px;
    color: #333;
}

.user-details p {
    margin: 5px 0 0;
    color: #666;
}

/* Automation Control Section */
.automation-control-section {
    background: white;
    border-radius: 10px;
    padding: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.automation-control-section h3 {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 18px;
    color: #333;
}

.control-info {
    color: #666;
    font-size: 14px;
    margin-bottom: 16px;
}

.control-buttons {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    margin-bottom: 12px;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-success {
    background: #34a853;
    color: white;
}

.btn-success:hover {
    background: #2d9348;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.automation-status {
    text-align: center;
    font-size: 14px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    margin-top: 8px;
}

#status-text {
    font-weight: bold;
}

.status-running {
    color: #34a853;
}

.status-paused {
    color: #ffc107;
}

.status-stopped {
    color: #dc3545;
}

.tickets-section {
    background: white;
    border-radius: 10px;
    padding: 12px;
    margin-bottom: 16px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.tickets-count-display {
    display: flex;
    align-items: center;
    gap: 15px;
}

.count-badge {
    background: #4285f4;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 18px;
    font-weight: bold;
    min-width: 40px;
    text-align: center;
}

.tickets-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.tickets-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.btn {
    padding: 10px 20px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #4285f4;
    color: white;
}

.btn-success {
    background: #34a853;
    color: white;
    width: 100%;
    justify-content: center;
}

.booking-form {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #333;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.logout-btn {
    width: 100%;
    padding: 12px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.logout-btn:hover {
    background: #c82333;
}

/* Booked Tickets Section */
.booked-tickets-section, .saved-tickets-section {
    background: white;
    border-radius: 10px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.booked-tickets-section h3, .saved-tickets-section h3 {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    color: #333;
}

.tickets-container {
    max-height: 300px;
    overflow-y: auto;
    padding-right: 5px;
}

.ticket-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    border-left: 4px solid #4285f4;
    position: relative;
}

.ticket-card.cancelled {
    border-left-color: #dc3545;
    opacity: 0.7;
}

.ticket-card.confirmed {
    border-left-color: #34a853;
}

.ticket-card.completed {
    border-left-color: #9e9e9e;
    opacity: 0.7;
}

.ticket-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.ticket-route {
    font-weight: bold;
    font-size: 16px;
}

.ticket-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    text-transform: uppercase;
    font-weight: bold;
}

.ticket-status.booked {
    background: #e3f2fd;
    color: #1976d2;
}

.ticket-status.confirmed {
    background: #e8f5e9;
    color: #2e7d32;
}

.ticket-status.cancelled {
    background: #ffebee;
    color: #c62828;
}

.ticket-status.completed {
    background: #f5f5f5;
    color: #616161;
}

.ticket-details {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #666;
}

.ticket-date, .ticket-class, .ticket-pnr {
    margin-right: 12px;
}

.ticket-actions {
    margin-top: 8px;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.btn-cancel {
    background: #f8d7da;
    color: #721c24;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.btn-cancel:hover {
    background: #f5c6cb;
}

.btn-view-details {
    background: #e3f2fd;
    color: #1976d2;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.btn-view-details:hover {
    background: #bbdefb;
}

/* Saved ticket card styles */
.ticket-card.saved-ticket {
    border-left-color: #673ab7;
}

.ticket-train {
    font-size: 12px;
    color: #666;
}

/* Action buttons for saved tickets */
.btn-rebook {
    background: #e8f5e9;
    color: #2e7d32;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.btn-rebook:hover {
    background: #c8e6c9;
}

.btn-edit {
    background: #e3f2fd;
    color: #1976d2;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.btn-edit:hover {
    background: #bbdefb;
}

.btn-delete {
    background: #ffebee;
    color: #c62828;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.btn-delete:hover {
    background: #ffcdd2;
}

.loading-message, .no-tickets-message {
    text-align: center;
    padding: 20px;
    color: #666;
}

.no-tickets-message i {
    font-size: 32px;
    margin-bottom: 8px;
    color: #ccc;
}

/* Payment Section */
.payment-section {
    background: white;
    border-radius: 10px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.payment-section h3 {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 18px;
    color: #333;
}

.payment-section p {
    margin-top: 0;
    margin-bottom: 16px;
    color: #666;
}

.payment-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.payment-option {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.payment-option:hover {
    border-color: #4285f4;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.ticket-count {
    font-weight: bold;
    margin-bottom: 4px;
}

.price {
    color: #4285f4;
    font-size: 18px;
    font-weight: bold;
}

.discount-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: #fbbc05;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 0 0 0 8px;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .payment-options {
        grid-template-columns: 1fr;
    }
}

/* Additional styles for clarity */
.booking-info {
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 14px;
    color: #666;
}

.small-text {
    font-size: 13px;
    color: #777;
    margin-top: 5px;
}

.payment-info {
    margin-top: 16px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    font-size: 13px;
    color: #666;
}

.payment-info i {
    color: #4285f4;
    margin-right: 5px;
}

/* Booking buttons */
.booking-buttons {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.btn-primary {
    background-color: #4285f4;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
}

.btn-primary:hover {
    background-color: #3367d6;
}

/* Tooltip styles */
.tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}
