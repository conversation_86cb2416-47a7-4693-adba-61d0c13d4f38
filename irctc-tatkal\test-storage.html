<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Storage Test - IRCTC Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #2980b9;
        }
        .output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            color: #e74c3c;
        }
        .success {
            color: #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>IRCTC Extension Storage Test</h1>
        
        <div class="section">
            <h3>Storage Operations</h3>
            <button onclick="checkStorage()">Check All Storage</button>
            <button onclick="checkSavedTickets()">Check Saved Tickets</button>
            <button onclick="addTestTicket()">Add Test Ticket</button>
            <button onclick="clearAllTickets()">Clear All Tickets</button>
        </div>
        
        <div class="section">
            <h3>Navigation</h3>
            <button onclick="goToBooking()">Go to Booking Page</button>
            <button onclick="goToSavedTickets()">Go to Saved Tickets</button>
        </div>
        
        <div class="section">
            <h3>Output</h3>
            <div id="output" class="output">Click a button to test storage functionality...</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function checkStorage() {
            log('Checking all storage data...');
            
            if (!chrome || !chrome.storage) {
                log('ERROR: Chrome storage API not available!', 'error');
                return;
            }
            
            chrome.storage.local.get(null, function(result) {
                if (chrome.runtime.lastError) {
                    log('ERROR: ' + chrome.runtime.lastError.message, 'error');
                    return;
                }
                
                log('All storage data:');
                log(JSON.stringify(result, null, 2));
                
                const keys = Object.keys(result);
                log(`Total storage keys: ${keys.length}`, 'success');
                keys.forEach(key => {
                    log(`- ${key}: ${typeof result[key]} (${Array.isArray(result[key]) ? result[key].length + ' items' : 'single value'})`);
                });
            });
        }

        function checkSavedTickets() {
            log('Checking saved tickets...');
            
            chrome.storage.local.get(['savedTickets'], function(result) {
                if (chrome.runtime.lastError) {
                    log('ERROR: ' + chrome.runtime.lastError.message, 'error');
                    return;
                }
                
                const savedTickets = result.savedTickets || [];
                log(`Found ${savedTickets.length} saved tickets`, 'success');
                
                if (savedTickets.length > 0) {
                    log('Saved tickets data:');
                    savedTickets.forEach((ticket, index) => {
                        log(`Ticket ${index + 1}:`);
                        log(`  Route: ${ticket.journey_details?.from || 'Unknown'} → ${ticket.journey_details?.destination || 'Unknown'}`);
                        log(`  Date: ${ticket.journey_details?.date || 'Unknown'}`);
                        log(`  Saved: ${ticket.savedAt || 'Unknown'}`);
                        log(`  Passengers: ${ticket.passengers?.length || 0}`);
                    });
                } else {
                    log('No saved tickets found');
                }
            });
        }

        function addTestTicket() {
            log('Adding test ticket...');
            
            const testTicket = {
                id: Date.now(),
                savedAt: new Date().toISOString(),
                irctc_credentials: {
                    user_name: '<EMAIL>',
                    password: 'test123'
                },
                journey_details: {
                    from: 'NEW DELHI',
                    destination: 'MUMBAI CENTRAL',
                    date: '2024-01-15',
                    class: 'SL',
                    quota: 'GN',
                    train_number: '12951',
                    boarding_station: 'NEW DELHI'
                },
                passengers: [{
                    name: 'Test User',
                    age: '30',
                    gender: 'M',
                    berth_choice: 'No Preference',
                    food_choice: 'Veg'
                }],
                infants: [],
                other_preferences: {
                    mobileNumber: '9876543210',
                    autoCaptcha: true,
                    autoUpgradation: false,
                    confirmberths: false,
                    paymentmethod: 'IRCUPI'
                },
                travel_preferences: {
                    travelInsuranceOpted: 'no',
                    prefcoach: '',
                    reservationchoice: 'LOWER'
                },
                paymentMethod: 'irctc-qr'
            };
            
            chrome.storage.local.get(['savedTickets'], function(result) {
                const savedTickets = result.savedTickets || [];
                savedTickets.push(testTicket);
                
                chrome.storage.local.set({ 'savedTickets': savedTickets }, function() {
                    if (chrome.runtime.lastError) {
                        log('ERROR: Failed to save test ticket - ' + chrome.runtime.lastError.message, 'error');
                        return;
                    }
                    
                    log('Test ticket added successfully!', 'success');
                    log(`Total tickets now: ${savedTickets.length}`);
                    checkSavedTickets();
                });
            });
        }

        function clearAllTickets() {
            log('Clearing all tickets...');
            
            chrome.storage.local.set({ 'savedTickets': [] }, function() {
                if (chrome.runtime.lastError) {
                    log('ERROR: Failed to clear tickets - ' + chrome.runtime.lastError.message, 'error');
                    return;
                }
                
                log('All tickets cleared successfully!', 'success');
                checkSavedTickets();
            });
        }

        function goToBooking() {
            window.location.href = 'book-ticket.html';
        }

        function goToSavedTickets() {
            window.location.href = 'saved-tickets.html';
        }

        // Auto-check storage on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Storage test page loaded');
            log('Chrome extension context: ' + (chrome && chrome.storage ? 'Available' : 'Not available'));
            
            if (chrome && chrome.storage) {
                checkSavedTickets();
            }
        });
    </script>
</body>
</html>
