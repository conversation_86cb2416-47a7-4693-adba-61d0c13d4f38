// Saved Tickets Management
document.addEventListener('DOMContentLoaded', function() {
    console.log('Saved Tickets page loaded');
    
    // Load and display saved tickets
    loadSavedTickets();
    
    // Event listeners
    setupEventListeners();
});

function setupEventListeners() {
    // Back to booking button
    document.getElementById('back-to-booking').addEventListener('click', function() {
        window.location.href = 'book-ticket.html';
    });
    
    // Create new ticket button
    document.getElementById('create-ticket').addEventListener('click', function() {
        window.location.href = 'book-ticket.html';
    });
    
    // Clear all tickets button
    document.getElementById('clear-all-tickets').addEventListener('click', function() {
        showClearAllModal();
    });
    
    // Modal close buttons
    document.getElementById('modal-close').addEventListener('click', function() {
        hideDeleteModal();
    });
    
    document.getElementById('clear-modal-close').addEventListener('click', function() {
        hideClearAllModal();
    });
    
    // Modal action buttons
    document.getElementById('confirm-delete').addEventListener('click', function() {
        confirmDeleteTicket();
    });
    
    document.getElementById('cancel-delete').addEventListener('click', function() {
        hideDeleteModal();
    });
    
    document.getElementById('confirm-clear-all').addEventListener('click', function() {
        confirmClearAllTickets();
    });
    
    document.getElementById('cancel-clear-all').addEventListener('click', function() {
        hideClearAllModal();
    });
    
    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        const deleteModal = document.getElementById('delete-modal');
        const clearAllModal = document.getElementById('clear-all-modal');
        
        if (event.target === deleteModal) {
            hideDeleteModal();
        }
        if (event.target === clearAllModal) {
            hideClearAllModal();
        }
    });
}

function loadSavedTickets() {
    console.log('Loading saved tickets...');
    
    chrome.storage.local.get(['savedTickets'], function(result) {
        const savedTickets = result.savedTickets || [];
        console.log('Found saved tickets:', savedTickets);
        
        displayTickets(savedTickets);
    });
}

function displayTickets(tickets) {
    const ticketsList = document.getElementById('tickets-list');
    const noTicketsMessage = document.getElementById('no-tickets-message');
    
    if (tickets.length === 0) {
        noTicketsMessage.style.display = 'block';
        ticketsList.style.display = 'none';
        return;
    }
    
    noTicketsMessage.style.display = 'none';
    ticketsList.style.display = 'grid';
    
    ticketsList.innerHTML = '';
    
    tickets.forEach((ticket, index) => {
        const ticketCard = createTicketCard(ticket, index);
        ticketsList.appendChild(ticketCard);
    });
}

function createTicketCard(ticket, index) {
    const card = document.createElement('div');
    card.className = 'ticket-card';
    card.setAttribute('data-index', index);
    
    const route = `${ticket.journey_details.from} → ${ticket.journey_details.destination}`;
    const date = new Date(ticket.savedAt).toLocaleDateString();
    const time = new Date(ticket.savedAt).toLocaleTimeString();
    
    card.innerHTML = `
        <div class="ticket-header">
            <div class="ticket-route">${route}</div>
            <div class="ticket-date">Saved: ${date}</div>
        </div>
        
        <div class="ticket-details">
            <div class="ticket-detail-row">
                <span class="ticket-detail-label">Journey Date:</span>
                <span class="ticket-detail-value">${ticket.journey_details.date}</span>
            </div>
            <div class="ticket-detail-row">
                <span class="ticket-detail-label">Class:</span>
                <span class="ticket-detail-value">${ticket.journey_details.class}</span>
            </div>
            <div class="ticket-detail-row">
                <span class="ticket-detail-label">Quota:</span>
                <span class="ticket-detail-value">${ticket.journey_details.quota}</span>
            </div>
            <div class="ticket-detail-row">
                <span class="ticket-detail-label">Passengers:</span>
                <span class="ticket-detail-value">${ticket.passengers.length}</span>
            </div>
            <div class="ticket-detail-row">
                <span class="ticket-detail-label">Payment:</span>
                <span class="ticket-detail-value">${getPaymentMethodName(ticket.paymentMethod)}</span>
            </div>
        </div>
        
        <div class="ticket-actions">
            <button class="btn-secondary edit-ticket" data-index="${index}">
                <i class="fas fa-edit"></i> Edit
            </button>
            <button class="btn-primary book-ticket" data-index="${index}">
                <i class="fas fa-ticket-alt"></i> Book
            </button>
            <button class="btn-danger delete-ticket" data-index="${index}">
                <i class="fas fa-trash"></i> Delete
            </button>
        </div>
    `;
    
    // Add event listeners to action buttons
    const editBtn = card.querySelector('.edit-ticket');
    const bookBtn = card.querySelector('.book-ticket');
    const deleteBtn = card.querySelector('.delete-ticket');
    
    editBtn.addEventListener('click', () => editTicket(index));
    bookBtn.addEventListener('click', () => bookTicket(index));
    deleteBtn.addEventListener('click', () => showDeleteModal(index));
    
    return card;
}

function getPaymentMethodName(method) {
    const methodNames = {
        'irctc-qr': 'IRCTC QR Code',
        'upi': 'UPI',
        'card': 'Credit/Debit Card',
        'netbanking': 'Net Banking'
    };
    return methodNames[method] || method;
}

function editTicket(index) {
    console.log('Editing ticket at index:', index);
    
    chrome.storage.local.get(['savedTickets'], function(result) {
        const savedTickets = result.savedTickets || [];
        const ticket = savedTickets[index];
        
        if (ticket) {
            // Store the ticket data for editing
            chrome.storage.local.set({ 'editingTicket': ticket }, function() {
                // Navigate to booking page
                window.location.href = 'book-ticket.html?edit=true';
            });
        }
    });
}

function bookTicket(index) {
    console.log('Booking ticket at index:', index);
    
    chrome.storage.local.get(['savedTickets'], function(result) {
        const savedTickets = result.savedTickets || [];
        const ticket = savedTickets[index];
        
        if (ticket) {
            // Store all the ticket data in the main storage keys
            const dataToStore = {
                'irctc_credentials': ticket.irctc_credentials,
                'journey_details': ticket.journey_details,
                'passengers': ticket.passengers,
                'infants': ticket.infants,
                'other_preferences': ticket.other_preferences,
                'travel_preferences': ticket.travel_preferences,
                'cardDetails': ticket.cardDetails,
                'upiId': ticket.upiId,
                'bankName': ticket.bankName,
                'hdfcPassword': ticket.hdfcPassword
            };
            
            chrome.storage.local.set(dataToStore, function() {
                console.log('Ticket data loaded for booking');
                
                // Send start booking request to background script
                chrome.runtime.sendMessage({
                    action: 'startBooking'
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.error('Failed to start booking:', chrome.runtime.lastError.message);
                        alert('Failed to start booking process. Please try again.');
                    } else if (response && response.status === 'success') {
                        console.log('Booking process started successfully');
                        alert('Booking automation started! The IRCTC website will open and automation will begin automatically.');
                    } else {
                        console.error('Background script returned error:', response);
                        alert('Failed to start booking automation: ' + (response?.message || 'Unknown error'));
                    }
                });
            });
        }
    });
}

function showDeleteModal(index) {
    console.log('Showing delete modal for index:', index);
    
    chrome.storage.local.get(['savedTickets'], function(result) {
        const savedTickets = result.savedTickets || [];
        const ticket = savedTickets[index];
        
        if (ticket) {
            // Store the index for deletion
            window.deleteTicketIndex = index;
            
            // Show ticket preview in modal
            const preview = document.getElementById('delete-ticket-preview');
            const route = `${ticket.journey_details.from} → ${ticket.journey_details.destination}`;
            
            preview.innerHTML = `
                <div class="ticket-detail-row">
                    <span class="ticket-detail-label">Route:</span>
                    <span class="ticket-detail-value">${route}</span>
                </div>
                <div class="ticket-detail-row">
                    <span class="ticket-detail-label">Date:</span>
                    <span class="ticket-detail-value">${ticket.journey_details.date}</span>
                </div>
                <div class="ticket-detail-row">
                    <span class="ticket-detail-label">Class:</span>
                    <span class="ticket-detail-value">${ticket.journey_details.class}</span>
                </div>
            `;
            
            document.getElementById('delete-modal').style.display = 'block';
        }
    });
}

function hideDeleteModal() {
    document.getElementById('delete-modal').style.display = 'none';
    window.deleteTicketIndex = null;
}

function confirmDeleteTicket() {
    const index = window.deleteTicketIndex;
    
    if (index !== null && index !== undefined) {
        chrome.storage.local.get(['savedTickets'], function(result) {
            const savedTickets = result.savedTickets || [];
            
            // Remove the ticket at the specified index
            savedTickets.splice(index, 1);
            
            // Save updated tickets
            chrome.storage.local.set({ 'savedTickets': savedTickets }, function() {
                console.log('Ticket deleted successfully');
                hideDeleteModal();
                loadSavedTickets(); // Reload the list
            });
        });
    }
}

function showClearAllModal() {
    document.getElementById('clear-all-modal').style.display = 'block';
}

function hideClearAllModal() {
    document.getElementById('clear-all-modal').style.display = 'none';
}

function confirmClearAllTickets() {
    chrome.storage.local.set({ 'savedTickets': [] }, function() {
        console.log('All tickets cleared');
        hideClearAllModal();
        loadSavedTickets(); // Reload the list
    });
}
