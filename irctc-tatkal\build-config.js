#!/usr/bin/env node

/**
 * Build Configuration Script for Chrome Extension
 * This script updates the config.js file based on the target environment
 */

const fs = require('fs');
const path = require('path');

// Get environment from command line argument
const environment = process.argv[2] || 'development';

if (!['development', 'production'].includes(environment)) {
    console.error('❌ Invalid environment. Use "development" or "production"');
    process.exit(1);
}

// Configuration for different environments
const configs = {
    development: {
        API_BASE_URL: 'http://localhost:3000/api',
        PAYMENT_URL: 'http://localhost:3000/payment',
        ENVIRONMENT: 'development'
    },
    production: {
        API_BASE_URL: 'https://ex-irctc.onrender.com/api',
        PAYMENT_URL: 'https://your-netlify-app.netlify.app/payment',
        ENVIRONMENT: 'production'
    }
};

// Generate the config.js content
const configContent = `// Environment Configuration for Chrome Extension
// Auto-generated by build-config.js - DO NOT EDIT MANUALLY

const CONFIG = {
    development: {
        API_BASE_URL: '${configs.development.API_BASE_URL}',
        PAYMENT_URL: '${configs.development.PAYMENT_URL}',
        ENVIRONMENT: '${configs.development.ENVIRONMENT}'
    },
    production: {
        API_BASE_URL: '${configs.production.API_BASE_URL}',
        PAYMENT_URL: '${configs.production.PAYMENT_URL}',
        ENVIRONMENT: '${configs.production.ENVIRONMENT}'
    }
};

// Current environment configuration
const CURRENT_ENV = '${environment}';
const API_CONFIG = CONFIG[CURRENT_ENV];

// Helper functions
const getApiUrl = (endpoint) => {
    return \`\${API_CONFIG.API_BASE_URL}\${endpoint}\`;
};

const getPaymentUrl = (params = '') => {
    return \`\${API_CONFIG.PAYMENT_URL}\${params}\`;
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { API_CONFIG, getApiUrl, getPaymentUrl };
} else {
    // For browser environment
    window.API_CONFIG = API_CONFIG;
    window.getApiUrl = getApiUrl;
    window.getPaymentUrl = getPaymentUrl;
}

console.log('🚀 Chrome Extension loaded with', CURRENT_ENV, 'configuration');
console.log('📡 API Base URL:', API_CONFIG.API_BASE_URL);
console.log('💳 Payment URL:', API_CONFIG.PAYMENT_URL);
`;

// Write the config file
const configPath = path.join(__dirname, 'config.js');

try {
    fs.writeFileSync(configPath, configContent);
    console.log(`✅ Successfully generated config.js for ${environment} environment`);
    console.log(`📁 Config file: ${configPath}`);
    console.log(`🔗 API URL: ${configs[environment].API_BASE_URL}`);
    console.log(`💳 Payment URL: ${configs[environment].PAYMENT_URL}`);
} catch (error) {
    console.error('❌ Error writing config file:', error.message);
    process.exit(1);
}
