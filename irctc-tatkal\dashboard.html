<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IRCTC Auto Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.2/font/bootstrap-icons.css">
    <link rel="stylesheet" href="styles/dashboard.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- User Profile Section -->
        <div class="user-profile-section">
            <img id="user-avatar" class="user-avatar" src="" alt="User Avatar">
            <div class="user-details">
                <h2 id="user-name">Loading...</h2>
                <p id="user-email">Loading...</p>
            </div>
            <div class="profile-menu">
                <button class="menu-toggle" id="profile-menu-toggle">
                    <i class="fa-solid fa-ellipsis-vertical"></i>
                </button>
                <div class="dropdown-menu" id="profile-dropdown">
                    <button class="dropdown-item" id="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </button>
                </div>
            </div>
        </div>

        <!-- Tickets Section -->
        <div class="tickets-section">
            <div class="tickets-header">
                <div class="tickets-count-display">
                    <h3>Available Booking Credits</h3>
                    <div class="count-badge" id="tickets-count">0</div>
                </div>
                <button id="add-tickets-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Buy More Credits
                </button>
            </div>
            <a href="#" id="detailed-booking-btn" class="btn btn-primary">
                <i class="fas fa-list-alt"></i> Detailed Booking
            </a>     
        </div>
        <!-- Payment Section -->
        <div class="payment-section">
            <h3>Purchase Booking Credits</h3>
            <p>Each credit allows you to book one train ticket with our auto-booking system.</p>
            <div class="payment-options">
                <a href="#" class="payment-option" data-quantity="1">
                    <span class="ticket-count">1 Credit</span>
                    <span class="price">₹99</span>
                </a>
                <a href="#" class="payment-option" data-quantity="5">
                    <span class="ticket-count">5 Credits</span>
                    <span class="price">₹449</span>
                    <span class="discount-badge">Save 10%</span>
                </a>
                <a href="#" class="payment-option" data-quantity="10">
                    <span class="ticket-count">10 Credits</span>
                    <span class="price">₹799</span>
                    <span class="discount-badge">Save 20%</span>
                </a>
            </div>
            <div class="payment-info">
                <p><i class="fas fa-info-circle"></i> Credits are used for our auto-booking system and are different from actual train tickets.</p>
            </div>
        </div>

    </div>
    <script src="config.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>