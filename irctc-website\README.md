# IRCTC Tatkal Extension Website

A React.js website for the IRCTC Tatkal booking extension, featuring user authentication, credit management, and admin panel.

## Features

### User Features
- **Google & Facebook OAuth Login** - Secure authentication
- **Credit Purchase System** - Buy credits to use with the extension
- **User Dashboard** - View available credits and booking history
- **API Key Management** - Get API key for extension authentication
- **Responsive Design** - Works on all devices

### Admin Features
- **Admin Dashboard** - Overview of users and statistics
- **User Management** - View, edit, and manage user accounts
- **Credit Management** - Add credits to user accounts
- **Status Management** - Activate, suspend, or delete users
- **Real-time Statistics** - Monitor platform usage

## Tech Stack

- **Frontend**: React.js 18, React Router DOM
- **Styling**: Custom CSS with responsive design
- **Authentication**: Google OAuth, Facebook Login
- **HTTP Client**: Axios
- **Backend**: Node.js/Express (separate repository)

## Prerequisites

- Node.js 16+ and npm
- Running backend server (see irctc-backend folder)
- Google OAuth credentials
- Facebook App credentials

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd chrome-ex
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   Environment files are already configured:
   - `.env.development` - For local development
   - `.env.production` - For Netlify deployment

4. **Start the development server**
   ```bash
   npm start
   ```

   The app will open at `http://localhost:3001`

## Backend Setup

### Development
Make sure the backend server is running:

1. Navigate to the backend directory:
   ```bash
   cd irctc-backend
   ```

2. Install dependencies and start:
   ```bash
   npm install
   npm run dev
   ```

   Backend will run on `http://localhost:3000`

### Production
Backend is deployed on Render at: `https://your-render-app.onrender.com`

## OAuth Setup

### Google OAuth
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized origins: `http://localhost:3001`
6. Update `REACT_APP_GOOGLE_CLIENT_ID` in `.env`

### Facebook Login
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add Facebook Login product
4. Configure Valid OAuth Redirect URIs: `http://localhost:3001`
5. Update `REACT_APP_FACEBOOK_APP_ID` in `.env`

## Project Structure

```
src/
├── components/           # React components
│   ├── LandingPage.js   # User landing page
│   ├── UserDashboard.js # User dashboard
│   ├── AdminLogin.js    # Admin login
│   ├── AdminPanel.js    # Admin dashboard
│   └── UserManagement.js # User management
├── services/            # API services
│   └── api.js          # API client and utilities
├── styles/             # CSS files
│   ├── index.css       # Global styles
│   ├── App.css         # App-level styles
│   ├── LandingPage.css # Landing page styles
│   ├── UserDashboard.css # Dashboard styles
│   ├── AdminLogin.css  # Admin login styles
│   ├── AdminPanel.css  # Admin panel styles
│   └── UserManagement.css # User management styles
├── App.js              # Main app component
└── index.js            # Entry point
```

## Available Scripts

- `npm start` - Start development server
- `npm build` - Build for production
- `npm test` - Run tests
- `npm eject` - Eject from Create React App

## API Endpoints

The frontend communicates with these backend endpoints:

### Authentication
- `POST /api/auth/google` - Google OAuth login
- `POST /api/auth/facebook` - Facebook OAuth login
- `POST /api/admin/login` - Admin login

### User Management
- `GET /api/tickets/count` - Get user's available credits
- `POST /api/tickets/purchase` - Purchase credits
- `GET /api/tickets/booked` - Get booking history

### Admin
- `GET /api/admin/stats` - Get dashboard statistics
- `GET /api/admin/users` - Get users list
- `GET /api/admin/users/:id` - Get user details
- `PATCH /api/admin/users/:id` - Update user
- `PATCH /api/admin/users/:id/status` - Update user status

## Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Netlify/Vercel
1. Build the project
2. Upload `build` folder to your hosting service
3. Configure environment variables
4. Set up redirects for React Router

### Environment Variables for Production
```env
REACT_APP_API_URL=https://your-backend-domain.com/api
REACT_APP_GOOGLE_CLIENT_ID=your_production_google_client_id
REACT_APP_FACEBOOK_APP_ID=your_production_facebook_app_id
```

## Features in Detail

### User Flow
1. User visits landing page
2. Signs in with Google/Facebook
3. Redirected to dashboard
4. Can purchase credits
5. Gets API key for extension
6. Views booking history

### Admin Flow
1. Admin visits `/admin/login`
2. Signs in with email/password
3. Views dashboard with statistics
4. Manages users and credits
5. Can add credits to user accounts

## Security Features

- JWT token authentication
- Protected routes
- Input validation
- CORS protection
- Rate limiting (backend)
- Secure credential storage

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, email <EMAIL> or create an issue in the repository.
