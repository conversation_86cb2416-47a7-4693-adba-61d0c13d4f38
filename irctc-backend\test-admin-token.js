require('dotenv').config();
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
const Admin = require('./models/Admin');
const connectDB = require('./config/db');
const fetch = require('node-fetch');

// Connect to MongoDB
connectDB();

const testAdminToken = async () => {
    try {
        // Find the admin user
        const admin = await Admin.findOne({ email: '<EMAIL>' });
        
        if (!admin) {
            console.log('Admin not found. Please run create-admin.js first.');
            return;
        }
        
        console.log('Admin found:', {
            id: admin._id,
            name: admin.name,
            email: admin.email
        });
        
        // Create a token for the admin
        const token = jwt.sign(
            { id: admin._id },
            process.env.JWT_SECRET || 'your-secret-key-here',
            { expiresIn: '1d' }
        );
        
        console.log('Admin token created:', token);
        
        // Test the users endpoint with the token
        console.log('\nTesting users endpoint with token...');
        try {
            const response = await fetch('http://localhost:3000/api/admin/users?page=1&limit=10', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            console.log('Response status:', response.status);
            const data = await response.json();
            console.log('Response data:', JSON.stringify(data, null, 2));
        } catch (error) {
            console.error('Error fetching users:', error);
        }
        
    } catch (error) {
        console.error('Error testing admin token:', error);
    } finally {
        // Close the connection
        mongoose.connection.close();
        console.log('Database connection closed');
    }
};

testAdminToken();
