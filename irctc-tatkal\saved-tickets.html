<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Saved Tickets - IRCTC Auto Booking</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="styles/saved-tickets.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-bookmark"></i> Saved Tickets</h1>
            <div class="header-actions">
                <button id="back-to-booking" class="btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Booking
                </button>
                <button id="clear-all-tickets" class="btn-danger">
                    <i class="fas fa-trash-alt"></i> Clear All
                </button>
            </div>
        </header>

        <main>
            <div class="tickets-container">
                <div id="no-tickets-message" class="no-tickets">
                    <i class="fas fa-bookmark"></i>
                    <h3>No Saved Tickets</h3>
                    <p>You haven't saved any tickets yet. Save your booking details to quickly access them later.</p>
                    <button id="create-ticket" class="btn-primary">
                        <i class="fas fa-plus"></i> Create New Ticket
                    </button>
                </div>

                <div id="tickets-list" class="tickets-list">
                    <!-- Saved tickets will be dynamically loaded here -->
                </div>
            </div>
        </main>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-exclamation-triangle"></i> Confirm Delete</h3>
                <button class="modal-close" id="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this saved ticket?</p>
                <div class="ticket-preview" id="delete-ticket-preview">
                    <!-- Ticket preview will be shown here -->
                </div>
            </div>
            <div class="modal-footer">
                <button id="confirm-delete" class="btn-danger">
                    <i class="fas fa-trash"></i> Delete
                </button>
                <button id="cancel-delete" class="btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </button>
            </div>
        </div>
    </div>

    <!-- Clear All Confirmation Modal -->
    <div id="clear-all-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-exclamation-triangle"></i> Clear All Tickets</h3>
                <button class="modal-close" id="clear-modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete all saved tickets? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button id="confirm-clear-all" class="btn-danger">
                    <i class="fas fa-trash-alt"></i> Clear All
                </button>
                <button id="cancel-clear-all" class="btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </button>
            </div>
        </div>
    </div>

    <script src="saved-tickets.js"></script>
</body>
</html>
