<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book IRCTC Train Ticket</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="styles/book-ticket.css">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-train"></i> IRCTC Auto Booking</h1>
            <div class="user-info" id="user-info">
                <img id="user-avatar" class="user-avatar" src="" alt="User Avatar">
                <div>
                    <span id="user-name">Loading...</span>
                    <span id="credits-badge"><i class="fas fa-ticket-alt"></i> <span id="credits-count">0</span> credits</span>
                </div>
            </div>
        </header>

        <main>
            <div class="booking-card">
                <div class="card-header">
                    <h2>Book Train Ticket</h2>
                    <p class="booking-info">This will use 1 booking credit from your account</p>
                </div>

                <form id="booking-form">
                    <!-- IRCTC Login Details -->
                    <div class="form-section">
                        <h3>IRCTC Login Details</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="irctc-username">IRCTC Username*</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-user"></i>
                                    <input type="text" id="irctc-username" placeholder="Enter IRCTC username" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="irctc-password">IRCTC Password*</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-lock"></i>
                                    <input type="password" id="irctc-password" placeholder="Enter IRCTC password" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Journey Details -->
                    <div class="form-section">
                        <h3>Journey Details</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="from-station">From Station*</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <input type="text" id="from-station" list="stations-list" placeholder="e.g. NEW DELHI (NDLS)" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="to-station">To Station*</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <input type="text" id="to-station" list="stations-list" placeholder="e.g. MUMBAI CENTRAL (MMCT)" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="journey-date">Journey Date*</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-calendar-alt"></i>
                                    <input type="date" id="journey-date" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="train-number">Train Number/Name</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-train"></i>
                                    <input type="text" id="train-number" placeholder="e.g. 12301 or Rajdhani">
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="class">Travel Class*</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-couch"></i>
                                    <select id="class" required>
                                        <option value="">Select Class</option>
                                        <option value="1A">First AC (1A)</option>
                                        <option value="2A">Second AC (2A)</option>
                                        <option value="3A">Third AC (3A)</option>
                                        <option value="SL">Sleeper (SL)</option>
                                        <option value="CC">Chair Car (CC)</option>
                                        <option value="EC">Executive Class (EC)</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="quota">Quota*</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-ticket-alt"></i>
                                    <select id="quota" required>
                                        <option value="GN">General (GN)</option>
                                        <option value="TQ">Tatkal (TQ)</option>
                                        <option value="PT">Premium Tatkal (PT)</option>
                                        <option value="LD">Ladies (LD)</option>
                                        <option value="DF">Defense (DF)</option>
                                        <option value="PH">Physically Handicapped (PH)</option>
                                        <option value="FT">Foreign Tourist (FT)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="boarding-station">Boarding Station (if different)</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <input type="text" id="boarding-station" list="stations-list" placeholder="e.g. NEW DELHI (NDLS)">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="mobile-number">Mobile Number*</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-mobile-alt"></i>
                                    <input type="tel" id="mobile-number" placeholder="e.g. 9876543210" required>
                                </div>
                            </div>
                        </div>

                        <datalist id="stations-list">
                            <!-- Will be populated dynamically from stationlist.json -->
                        </datalist>
                    </div>

                    <!-- Availability Check -->
                    <div class="form-section">
                        <h3>Auto-Click Time Settings</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label>Quota Timing Presets</label>
                                <div class="quota-timing-info">
                                    <div class="quota-timing">
                                        <span class="quota-name">Sleeper (SL):</span>
                                        <span class="quota-time">10:59:59</span>
                                    </div>
                                    <div class="quota-timing">
                                        <span class="quota-name">AC Classes:</span>
                                        <span class="quota-time">09:59:59</span>
                                    </div>
                                    <div class="quota-timing">
                                        <span class="quota-name">General (GN):</span>
                                        <span class="quota-time">07:59:59</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="custom-time">Custom Auto-Click Time (optional)</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-clock"></i>
                                    <input type="time" id="custom-time" step="1">
                                </div>
                                <small>Leave empty to use preset timing based on quota</small>
                            </div>
                        </div>
                    </div>



                    <!-- Passenger Details -->
                    <div class="form-section">
                        <h3>Passenger Details (Up to 6 Passengers)</h3>
                        <div class="passenger-table-container">
                            <table class="passenger-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Name*</th>
                                        <th>Age*</th>
                                        <th>Gender*</th>
                                        <th>Berth</th>
                                        <th>Food</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="passengers-container">
                                    <tr class="passenger-row" data-passenger-index="0">
                                        <td>1</td>
                                        <td><input type="text" id="passenger-name-0" name="passenger-name-0" placeholder="Full name" required></td>
                                        <td><input type="number" id="passenger-age-0" name="passenger-age-0" min="1" max="120" placeholder="Age" required style="width: 50px;"></td>
                                        <td>
                                            <select id="passenger-gender-0" name="passenger-gender-0" required>
                                                <option value="">Select</option>
                                                <option value="M">Male</option>
                                                <option value="F">Female</option>
                                                <option value="O">Other</option>
                                            </select>
                                        </td>
                                        <td>
                                            <select id="passenger-berth-0" name="passenger-berth-0">
                                                <option value="">No Pref</option>
                                                <option value="LB">Lower</option>
                                                <option value="MB">Middle</option>
                                                <option value="UB">Upper</option>
                                                <option value="SL">Side Lower</option>
                                                <option value="SU">Side Upper</option>
                                            </select>
                                        </td>
                                        <td>
                                            <select id="passenger-food-0" name="passenger-food-0">
                                                <option value="">-</option>
                                                <option value="V">Veg</option>
                                                <option value="N">Non-Veg</option>
                                            </select>
                                        </td>
                                        <td>
                                            <button type="button" class="btn-icon remove-passenger" disabled><i class="fas fa-times"></i></button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <button type="button" id="add-passenger" class="btn-secondary btn-sm">
                            <i class="fas fa-plus"></i> Add Passenger
                        </button>
                    </div>

                    <!-- Infant Details -->
                    <div class="form-section">
                        <h3>Infants (Without Berth)</h3>
                        <div class="infant-table-container">
                            <table class="infant-table">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Name*</th>
                                        <th>Gender*</th>
                                        <th>Below 1 Year</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="infants-container">
                                    <!-- No infants by default -->
                                </tbody>
                            </table>
                            <p class="no-infants-message">No infants added. Click the button below to add an infant.</p>
                        </div>

                        <button type="button" id="add-infant" class="btn-secondary btn-sm">
                            <i class="fas fa-plus"></i> Add Infant
                        </button>
                    </div>



                    <!-- Payment Details -->
                    <div class="form-section">
                        <h3>Payment Details</h3>

                        <div class="payment-methods">
                            <div class="payment-method">
                                <input type="radio" id="payment-irctc-qr" name="payment-method" value="irctc-qr" checked>
                                <label for="payment-irctc-qr">
                                    <i class="fas fa-qrcode"></i>
                                    <span>IRCTC QR Code</span>
                                </label>
                            </div>

                            <div class="payment-method">
                                <input type="radio" id="payment-upi" name="payment-method" value="upi">
                                <label for="payment-upi">
                                    <i class="fas fa-mobile-alt"></i>
                                    <span>UPI</span>
                                </label>
                            </div>

                            <div class="payment-method">
                                <input type="radio" id="payment-card" name="payment-method" value="card">
                                <label for="payment-card">
                                    <i class="fas fa-credit-card"></i>
                                    <span>Credit/Debit Card</span>
                                </label>
                            </div>

                            <div class="payment-method">
                                <input type="radio" id="payment-netbanking" name="payment-method" value="netbanking">
                                <label for="payment-netbanking">
                                    <i class="fas fa-university"></i>
                                    <span>Net Banking</span>
                                </label>
                            </div>
                        </div>

                        <!-- Payment method details (shown/hidden based on selection) -->
                        <div class="payment-details" id="payment-details-container">
                            <!-- IRCTC QR Code (default) -->
                            <div class="payment-detail-section" id="irctc-qr-details">
                                <p class="payment-info">The system will automatically scan and process the IRCTC QR code during booking.</p>
                            </div>

                            <!-- UPI Details -->
                            <div class="payment-detail-section hidden" id="upi-details">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="upi-id">UPI ID*</label>
                                        <div class="input-with-icon">
                                            <i class="fas fa-at"></i>
                                            <input type="text" id="upi-id" placeholder="e.g. 9876543210@upi">
                                        </div>
                                        <small>Example: 9329xxxx45@paytm</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Card Details -->
                            <div class="payment-detail-section hidden" id="card-details">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="card-number">Card Number*</label>
                                        <div class="input-with-icon">
                                            <i class="fas fa-credit-card"></i>
                                            <input type="text" id="card-number" placeholder="XXXX XXXX XXXX XXXX">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="card-expiry">Expiry Date*</label>
                                        <div class="input-with-icon">
                                            <i class="fas fa-calendar-alt"></i>
                                            <input type="text" id="card-expiry" placeholder="MM/YY">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="card-cvv">CVV*</label>
                                        <div class="input-with-icon">
                                            <i class="fas fa-lock"></i>
                                            <input type="password" id="card-cvv" placeholder="XXX">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="card-name">Name on Card*</label>
                                        <div class="input-with-icon">
                                            <i class="fas fa-user"></i>
                                            <input type="text" id="card-name" placeholder="Enter name as on card">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Net Banking Details -->
                            <div class="payment-detail-section hidden" id="netbanking-details">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="bank-name">Select Bank*</label>
                                        <div class="input-with-icon">
                                            <i class="fas fa-university"></i>
                                            <select id="bank-name">
                                                <option value="">Select Bank</option>
                                                <option value="SBI">State Bank of India</option>
                                                <option value="HDFC">HDFC Bank</option>
                                                <option value="ICICI">ICICI Bank</option>
                                                <option value="AXIS">Axis Bank</option>
                                                <option value="PNB">Punjab National Bank</option>
                                                <option value="BOB">Bank of Baroda</option>
                                                <option value="CANARA">Canara Bank</option>
                                                <option value="KOTAK">Kotak Mahindra Bank</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- HDFC Static Password (shown only when HDFC is selected) -->
                                <div class="form-row hidden" id="hdfc-password-row">
                                    <div class="form-group">
                                        <label for="hdfc-password">HDFC Static Password*</label>
                                        <div class="input-with-icon">
                                            <i class="fas fa-lock"></i>
                                            <input type="password" id="hdfc-password" placeholder="Enter HDFC static password">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- Additional Choices -->
                    <div class="form-section">
                        <h3>Additional Choices</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="captcha-autofill">Captcha Auto-fill</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="captcha-autofill" name="captcha-autofill" checked>
                                    <label for="captcha-autofill"></label>
                                </div>
                                <small>Automatically fill captcha during booking</small>
                            </div>

                            <div class="form-group">
                                <label for="submit-mode">Submit Mode</label>
                                <select id="submit-mode" name="submit-mode">
                                    <option value="auto">Auto (Fully Automatic)</option>
                                    <option value="passenger">Submit Passenger Page Manually</option>
                                    <option value="payment">Submit Payment Page Manually</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="auto-upgrade">Consider Auto Upgradation</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="auto-upgrade" name="auto-upgrade">
                                    <label for="auto-upgrade"></label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="book-confirm-berth">Book Only If Confirm Berths</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="book-confirm-berth" name="book-confirm-berth">
                                    <label for="book-confirm-berth"></label>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="travel-insurance">Travel Insurance</label>
                                <div class="toggle-switch">
                                    <input type="checkbox" id="travel-insurance" name="travel-insurance">
                                    <label for="travel-insurance"></label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="coach-number">Preferred Coach Number</label>
                                <div class="input-with-icon">
                                    <i class="fas fa-subway"></i>
                                    <input type="text" id="coach-number" placeholder="e.g. B1, A2">
                                </div>
                            </div>
                        </div>
                    </div>





                    <!-- Form Actions -->
                    <div class="form-actions">
                        <div class="action-buttons">
                            <button type="button" id="clear-form" class="btn-danger">
                                <i class="fas fa-trash"></i> Clear Form
                            </button>
                            <!-- <button type="button" id="save-draft" class="btn-secondary">
                                <i class="fas fa-save"></i> Save Draft
                            </button> -->
                            <button type="button" id="save-ticket" class="btn-secondary">
                                <i class="fas fa-bookmark"></i> Save Ticket
                            </button>
                        </div>
                        <button type="submit" id="book-now" class="btn-primary">
                            <i class="fas fa-ticket-alt"></i> Book Ticket
                        </button>
                    </div>
                </form>
            </div>

            <div class="info-sidebar">
                <div class="info-card">
                    <h3><i class="fas fa-info-circle"></i> Booking Information</h3>
                    <ul>
                        <li>Each booking uses 1 credit from your account</li>
                        <li>Tatkal booking opens at 10:00 AM for AC classes</li>
                        <li>Tatkal booking opens at 11:00 AM for Non-AC classes</li>
                        <li>Our system will automatically attempt to book your ticket</li>
                        <li>You will receive email notification once booking is complete</li>
                    </ul>
                </div>

                <div class="info-card">
                    <h3><i class="fas fa-credit-card"></i> Your Credits</h3>
                    <div class="credits-info">
                        <p>Available Credits: <span id="sidebar-credits-count">0</span></p>
                        <a href="add-credits.html" id="buy-more-credits" class="btn-outline">
                            <i class="fas fa-plus"></i> Buy More Credits
                        </a>
                    </div>
                </div>

                <div class="info-card">
                    <h3><i class="fas fa-history"></i> Recent Bookings</h3>
                    <div id="recent-bookings">
                        <p class="loading-text">Loading recent bookings...</p>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <div class="footer-links">
                <a href="dashboard.html"><i class="fas fa-home"></i> Dashboard</a>
                <a href="#" id="help-link"><i class="fas fa-question-circle"></i> Help</a>
                <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
            <div class="copyright">
                &copy; 2023 IRCTC Auto Booking Extension
            </div>
        </footer>
    </div>

    <script src="config.js"></script>
    <script src="book-ticket.js"></script>
</body>
</html>
