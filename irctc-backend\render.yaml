services:
  - type: web
    name: irctc-backend
    env: node
    plan: free
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: MONGODB_URI
        sync: false  # Set this manually in Render dashboard
      - key: JWT_SECRET
        sync: false  # Set this manually in Render dashboard
      - key: GOOGLE_CLIENT_ID
        sync: false  # Set this manually in Render dashboard
      - key: GOOGLE_CLIENT_SECRET
        sync: false  # Set this manually in Render dashboard
      - key: RAZORPAY_KEY_ID
        sync: false  # Set this manually in Render dashboard
      - key: RAZORPAY_KEY_SECRET
        sync: false  # Set this manually in Render dashboard
      - key: FRONTEND_URL_PROD
        value: https://your-netlify-app.netlify.app
      - key: ADMIN_EMAIL
        value: <EMAIL>
      - key: ADMIN_PASSWORD
        sync: false  # Set this manually in Render dashboard
